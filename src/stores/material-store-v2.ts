import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  Material,
  MaterialAddRequest,
  MaterialDeleteRequest,
  MaterialGetRequest,
  MaterialListRequest,
  MaterialUpdateRequest} from "@/api/bcare-types-v2";
import { materialAdd, materialDelete, materialGet, materialList, materialUpdate } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  materials: Material[];
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const useMaterialStore = defineStore("material", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const materials = shallowRef<Material[]>([]);
  const currentMaterial = shallowRef<Material | null>(null);
  const filteredMaterials = shallowRef<Material[]>([]);

  const cachedData = useStorage<CacheData>("materialStoreCache", { materials: [], expireTime: 0 });

  // Getters
  const getMaterialCount = computed(() => materials.value.length);
  const getMaterialById = computed(() => (id: number) => materials.value.find((material) => material.id === id));
  const hasCachedData = computed(() => {
    return cachedData.value.expireTime > Date.now() && cachedData.value.materials.length > 0;
  });

  // Cache functions
  function initializeFromCache() {
    if (hasCachedData.value) {
      materials.value = cachedData.value.materials;
    }
  }

  function updateCache() {
    cachedData.value = {
      materials: materials.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  // Actions
  function fetchAllMaterials() {
    return performAsyncAction(async () => {
      const response = await materialList({ page: 1, page_size: 1000 });
      if (response.data?.materials) {
        materials.value = response.data.materials;
        updateCache();
      }
      return response.data;
    });
  }

  function addMaterial(req: MaterialAddRequest) {
    return performAsyncAction(async () => {
      const response = await materialAdd(req);
      if (response.data) {
        materials.value.push(response.data);
        updateCache();
      }
      return response.data;
    });
  }

  function deleteMaterial(req: MaterialDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await materialDelete(req);
      if (response.data) {
        materials.value = materials.value.filter((material) => material.id !== req.id);
        updateCache();
      }
      return response.data;
    });
  }

  function getMaterial(req: MaterialGetRequest) {
    return performAsyncAction(async () => {
      const response = await materialGet(req);
      currentMaterial.value = response.data ?? null;
      return response.data;
    });
  }

  function listMaterials(req: MaterialListRequest) {
    return performAsyncAction(async () => {
      const response = await materialList(req);
      if (response.data?.materials) {
        filteredMaterials.value = response.data.materials;
      }
      return response.data;
    });
  }

  function updateMaterial(req: MaterialUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await materialUpdate(req);
      if (response.data) {
        const index = materials.value.findIndex((material) => material.id === req.id);
        if (index !== -1) {
          materials.value[index] = response.data;
          updateCache();
        }
      }
      return response.data;
    });
  }

  function updateFullMaterialList() {
    return performAsyncAction(async () => {
      const response = await materialList({ page: 1, page_size: 1000 });
      if (response.data?.materials) {
        materials.value = response.data.materials;
        updateCache();
      }
      return response.data;
    });
  }

  return {
    // State
    materials,
    currentMaterial,
    filteredMaterials,
    isLoading,
    error,
    // Getters
    getMaterialCount,
    getMaterialById,
    hasCachedData,
    // Actions
    initializeFromCache,
    fetchAllMaterials,
    addMaterial,
    deleteMaterial,
    getMaterial,
    listMaterials,
    updateMaterial,
    updateFullMaterialList,
  };
});

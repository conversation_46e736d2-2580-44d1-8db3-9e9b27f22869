import { storeToRefs } from "pinia";
import { computed, onMounted, shallowRef } from "vue";

import type {
  Material,
  MaterialAddRequest,
  MaterialDeleteRequest,
  MaterialGetRequest,
  MaterialListRequest,
  MaterialUpdateRequest,
} from "@/api/bcare-types-v2";
import { useMaterialStore } from "@/stores/material-store-v2";

interface UseMaterialOptions {
  autoLoad?: boolean;
}

export default function useMaterial(options: UseMaterialOptions = { autoLoad: true }) {
  const materialStore = useMaterialStore();
  const { isLoading, error, materials } = storeToRefs(materialStore);

  // Local state for filtered materials, initialized as empty
  const filteredMaterials = shallowRef<Material[]>([]);

  // Computed property for material count
  const materialCount = computed(() => materialStore.getMaterialCount);

  // Load all materials
  const loadMaterials = async () => {
    if (materialStore.hasCachedData) {
      materialStore.initializeFromCache();
    } else {
      await materialStore.fetchAllMaterials();
    }
    // Keep filteredMaterials empty after loading
    filteredMaterials.value = [];
  };

  // Auto-load materials if autoLoad is true
  if (options.autoLoad) {
    onMounted(() => {
      loadMaterials();
    });
  }

  // Add a new material
  const addMaterial = async (req: MaterialAddRequest) => {
    return await materialStore.addMaterial(req);
  };

  // Delete a material
  const deleteMaterial = async (req: MaterialDeleteRequest) => {
    await materialStore.deleteMaterial(req);
  };

  // Get a specific material
  const getMaterial = async (req: MaterialGetRequest) => {
    return await materialStore.getMaterial(req);
  };

  // List materials with filtering
  const listMaterials = async (req: MaterialListRequest) => {
    const response = await materialStore.listMaterials(req);
    if (response?.materials) {
      filteredMaterials.value = response.materials;
    }
    return response;
  };

  // Update a material
  const updateMaterial = async (req: MaterialUpdateRequest) => {
    return await materialStore.updateMaterial(req);
  };

  // Get a material by ID
  const getMaterialById = (id: number) => {
    return materialStore.getMaterialById(id);
  };

  // Get multiple materials by their IDs
  const getMaterialsByIds = (ids: number[]) => {
    return ids.map((id) => materialStore.getMaterialById(id)).filter(Boolean) as Material[];
  };

  // Search materials
  const searchMaterials = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    filteredMaterials.value = materialStore.materials.filter(
      (material) =>
        material.name.toLowerCase().includes(lowercaseQuery) ||
        (material.code && material.code.toLowerCase().includes(lowercaseQuery)) ||
        material.unit.toLowerCase().includes(lowercaseQuery),
    );
  };

  // Sort materials
  const sortMaterials = (key: keyof Material, order: "asc" | "desc" = "asc") => {
    filteredMaterials.value = [...filteredMaterials.value].sort((a, b) => {
      if (a[key] < b[key]) return order === "asc" ? -1 : 1;
      if (a[key] > b[key]) return order === "asc" ? 1 : -1;
      return 0;
    });
  };

  // Paginate materials
  const paginateMaterials = (page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    return filteredMaterials.value.slice(startIndex, startIndex + pageSize);
  };

  const getMaterialNameById = (id: number): string => {
    const material = materialStore.getMaterialById(id);
    return material ? material.name : "Unknown";
  };

  return {
    isLoading,
    error,
    materials, // Exposed materials list from the store
    filteredMaterials,
    materialCount,
    loadMaterials,
    addMaterial,
    deleteMaterial,
    getMaterial,
    listMaterials,
    updateMaterial,
    getMaterialById,
    getMaterialsByIds,
    searchMaterials,
    sortMaterials,
    paginateMaterials,
    getMaterialNameById,
  };
}

import * as types from "./bcare-types-v2";
import service from "./http";

/**
 * @description
 * @param req
 */
export function activityList(req: types.ListRequest) {
  return service.post<types.GenericResponse<types.ListResponse>>(`/v1/activity/list`, req);
}

/**
 * @description
 * @param req
 */
export function activityQuery(req: types.ActivityDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(`/v1/activity/query`, req);
}

/**
 * @description
 * @param req
 */
export function adminLogs(req: types.LogsRequest) {
  return service.post<types.GenericResponse<types.LogsResponse>>(`/v1/admin/logs`, req);
}

/**
 * @description
 */
export function adminMonitor() {
  return service.post<types.GenericResponse<types.MonitorResponse>>(`/v1/admin/monitor`);
}

/**
 * @description
 * @param req
 */
export function appointmentAdd(req: types.AppointmentAddRequest) {
  return service.post<types.GenericResponse<types.AppointmentResponse>>(`/v1/appointment/add`, req);
}

/**
 * @description
 * @param req
 */
export function appointmentDelete(req: types.AppointmentDeleteRequest) {
  return service.post<types.GenericResponse<types.AppointmentResponse>>(
    `/v1/appointment/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function appointmentGet(req: types.AppointmentGetRequest) {
  return service.post<types.GenericResponse<types.AppointmentResponse>>(`/v1/appointment/get`, req);
}

/**
 * @description
 * @param req
 */
export function appointmentGetLasted(req: types.AppointmentGetRequest) {
  return service.post<types.GenericResponse<types.AppointmentResponse>>(
    `/v1/appointment/get/next`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function appointmentList(req: types.AppointmentListRequest) {
  return service.post<types.GenericResponse<types.AppointmentListResponse>>(
    `/v1/appointment/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function appointmentQuery(req: types.AppointmentDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/appointment/query`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function appointmentUpdate(req: types.AppointmentUpdateRequest) {
  return service.post<types.GenericResponse<types.AppointmentResponse>>(
    `/v1/appointment/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentAdd(req: types.AttachmentAddRequest) {
  return service.post<types.GenericResponse<types.AttachmentResponse>>(`/v1/attachment/add`, req);
}

/**
 * @description
 * @param req
 */
export function attachmentChildren(req: types.AttachmentGetRequest) {
  return service.post<types.GenericResponse<Array<types.AttachmentResponse>>>(
    `/v1/attachment/children`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentDelete(req: types.AttachmentDeleteRequest) {
  return service.post<types.GenericResponse<types.AttachmentResponse>>(
    `/v1/attachment/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentForceUpdateCreatedAt(req: types.AttachmenForcetUpdateRequest) {
  return service.post<types.GenericResponse<types.AttachmentResponse>>(
    `/v1/attachment/force-created-at`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentGet(req: types.AttachmentGetRequest) {
  return service.post<types.GenericResponse<types.AttachmentTreeResponse>>(
    `/v1/attachment/get`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentList(req: types.AttachmentListRequest) {
  return service.post<types.GenericResponse<types.AttachmentListResponse>>(
    `/v1/attachment/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentQuery(req: types.AttachmentDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/attachment/query`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentQueryOperation(req: types.AttachmentDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/attachment/query-operation`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentUpdate(req: types.AttachmentUpdateRequest) {
  return service.post<types.GenericResponse<types.AttachmentResponse>>(
    `/v1/attachment/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachment_dataClear(req: types.ClearAttachmentDataRequest) {
  return service.post<types.GenericResponse<types.CommonResponse>>(
    `/v1/attachment/data/clear`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachment_dataGet(req: types.GetAttachmentDataRequest) {
  return service.post<types.GenericResponse<types.AttachmentDataResponse>>(
    `/v1/attachment/data/get`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachment_dataSet(req: types.SetAttachmentDataRequest) {
  return service.post<types.GenericResponse<types.CommonResponse>>(`/v1/attachment/data/set`, req);
}

/**
 * @description
 */
export function authGenerateCsrf() {
  return service.post<types.GenericResponse<types.CsrfTokenResponse>>(`/v1/auth/csrf-token`);
}

/**
 * @description
 * @param req
 */
export function authLogin(req: types.LoginRequest) {
  return service.post<types.GenericResponse<types.LoginResponse>>(`/v1/auth/login`, req);
}

/**
 * @description
 * @param req
 */
export function authRefresh(req: types.RefreshTokenRequest) {
  return service.post<types.GenericResponse<types.RefreshTokenResponse>>(`/v1/auth/refresh`, req);
}

/**
 * @description
 * @param req
 */
export function authVerify(req: types.VerifyRequest) {
  return service.post<types.GenericResponse<types.VerifyResponse>>(`/v1/auth/verify`, req);
}

/**
 * @description
 * @param req
 */
export function billAdd(req: types.BillAddRequest) {
  return service.post<types.GenericResponse<types.BillResponse>>(`/v1/bill/add`, req);
}

/**
 * @description
 * @param req
 */
export function billAddFromDeal(req: types.BillAddFromDealRequest) {
  return service.post<types.GenericResponse<types.BillResponse>>(`/v1/bill/add-from-deal`, req);
}

/**
 * @description
 * @param req
 */
export function billDelete(req: types.BillDeleteRequest) {
  return service.post<types.GenericResponse<types.BillResponse>>(`/v1/bill/delete`, req);
}

/**
 * @description
 * @param req
 */
export function billGet(req: types.BillGetRequest) {
  return service.post<types.GenericResponse<types.BillResponse>>(`/v1/bill/get`, req);
}

/**
 * @description
 * @param req
 */
export function billList(req: types.BillListRequest) {
  return service.post<types.GenericResponse<types.BillListResponse>>(`/v1/bill/list`, req);
}

/**
 * @description
 * @param req
 */
export function billQuery(req: types.BillDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(`/v1/bill/query`, req);
}

/**
 * @description
 * @param req
 */
export function billUpdate(req: types.BillUpdateRequest) {
  return service.post<types.GenericResponse<types.BillResponse>>(`/v1/bill/update`, req);
}

/**
 * @description
 * @param req
 */
export function bill_dataClear(req: types.ClearBillDataRequest) {
  return service.post<types.GenericResponse<types.CommonResponse>>(`/v1/bill/data/clear`, req);
}

/**
 * @description
 * @param req
 */
export function bill_dataGet(req: types.GetBillDataRequest) {
  return service.post<types.GenericResponse<types.BillDataResponse>>(`/v1/bill/data/get`, req);
}

/**
 * @description
 * @param req
 */
export function bill_dataSet(req: types.SetBillDataRequest) {
  return service.post<types.GenericResponse<types.CommonResponse>>(`/v1/bill/data/set`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemAdd(req: types.BillItemAddRequest) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(`/v1/bill-item/add`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemAddFromAttachment(req: types.BillItemAddFromAttachment) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(
    `/v1/bill-item/add-from-attachment`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function bill_itemDelete(req: types.BillItemDeleteRequest) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(`/v1/bill-item/delete`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemGet(req: types.BillItemGetRequest) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(`/v1/bill-item/get`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemList(req: types.BillItemListRequest) {
  return service.post<types.GenericResponse<types.BillItemListResponse>>(`/v1/bill-item/list`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemGetPaid(req: types.BillItemGetPaidRequest) {
  return service.post<types.GenericResponse<types.BillItemListResponse>>(`/v1/bill-item/paid`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemGetPartiallyPaid(req: types.BillItemGetPartiallyPaidRequest) {
  return service.post<types.GenericResponse<types.BillItemListResponse>>(
    `/v1/bill-item/partially-paid`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function bill_itemQuery(req: types.BillItemDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/bill-item/query`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function bill_itemUpdate(req: types.BillItemUpdateRequest) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(`/v1/bill-item/update`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemUpdateFromAttachment(req: types.BillItemAddFromAttachment) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(
    `/v1/bill-item/update-from-attachment`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function bundleAdd(req: types.BundleAddRequest) {
  return service.post<types.GenericResponse<types.BundleResponse>>(`/v1/bundle/add`, req);
}

/**
 * @description
 * @param req
 */
export function bundleDelete(req: types.BundleGetRequest) {
  return service.post<types.GenericResponse<types.BundleResponse>>(`/v1/bundle/delete`, req);
}

/**
 * @description
 * @param req
 */
export function bundleGet(req: types.BundleGetRequest) {
  return service.post<types.GenericResponse<types.BundleResponse>>(`/v1/bundle/get`, req);
}

/**
 * @description
 * @param req
 */
export function bundleList(req: types.BundleListRequest) {
  return service.post<types.GenericResponse<types.BundleListResponse>>(`/v1/bundle/list`, req);
}

/**
 * @description
 * @param req
 */
export function bundleUpdate(req: types.BundleUpdateRequest) {
  return service.post<types.GenericResponse<types.BundleResponse>>(`/v1/bundle/update`, req);
}

/**
 * @description
 * @param req
 */
export function callAccept(req: types.CallUpdateRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/accept`, req);
}

/**
 * @description
 * @param req
 */
export function callAdd(req: types.CallAddRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/add`, req);
}

/**
 * @description
 * @param req
 */
export function callDelete(req: types.CallDeleteRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/delete`, req);
}

/**
 * @description
 * @param req
 */
export function callEnd(req: types.CallEndRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/end`, req);
}

/**
 * @description
 * @param req
 */
export function callGet(req: types.CallUpdateRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/get`, req);
}

/**
 * @description
 * @param req
 */
export function callList(req: types.CallListRequest) {
  return service.post<types.GenericResponse<types.CallListResponse>>(`/v1/call/list`, req);
}

/**
 * @description
 * @param req
 */
export function callQuery(req: types.CallDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(`/v1/call/query`, req);
}

/**
 * @description
 * @param req
 */
export function callUpdateFeedback(req: types.CallUpdateFeedbackRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/update/feedback`, req);
}

/**
 * @description
 * @param req
 */
export function casbinRoleAdd(req: types.Role) {
  return service.post<types.GenericResponse<types.Role>>(`/v1/casbin/role/add`, req);
}

/**
 * @description
 */
export function casbinRoleDelete() {
  return service.post<types.GenericResponse<boolean>>(`/v1/casbin/role/delete`);
}

/**
 * @description
 */
export function casbinRoleList() {
  return service.post<types.GenericResponse<Array<types.Role>>>(`/v1/casbin/role/list`);
}

/**
 * @description
 */
export function casbinSync() {
  return service.post<types.GenericResponse<types.SyncResponse>>(`/v1/casbin/sync`);
}

/**
 * @description
 */
export function constantGet() {
  return service.post<types.GenericResponse<types.ConstantResponse>>(`/v1/constant`);
}

/**
 * @description
 * @param req
 */
export function dealAdd(req: types.DealAddRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/add`, req);
}

/**
 * @description
 * @param req
 */
export function dealCheckout(req: types.DealCheckoutRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/checkout`, req);
}

/**
 * @description
 * @param req
 */
export function dealDelete(req: types.DealDeleteRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/delete`, req);
}

/**
 * @description
 * @param req
 */
export function dealGet(req: types.DealGetRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/get`, req);
}

/**
 * @description
 * @param req
 */
export function dealList(req: types.DealListRequest) {
  return service.post<types.GenericResponse<types.DealListResponse>>(`/v1/deal/list`, req);
}

/**
 * @description
 * @param req
 */
export function dealListBig(req: types.DealListRequest) {
  return service.post<types.GenericResponse<types.DealListResponse>>(`/v1/deal/list-big`, req);
}

/**
 * @description
 * @param req
 */
export function dealUpdate(req: types.DealUpdateRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/update`, req);
}

/**
 * @description
 * @param req
 */
export function deal_stage_historyList(req: types.DealStageHistoryListRequest) {
  return service.post<types.GenericResponse<types.DealStageHistoryListResponse>>(
    `/v1/deal/history_stage/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function deal_stage_historyQuery(req: types.DealStageHistoryDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/deal/history_stage/query`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function deal_stage_historyUpdate(req: types.DealStageHistoryUpdateRequest) {
  return service.post<types.GenericResponse<types.DealStageHistory>>(
    `/v1/deal/history_stage/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function deal_userAddRating(req: types.DealUserRatingAddRequest) {
  return service.post<types.GenericResponse<types.DealUserRating>>(`/v1/deal_user/rating/add`, req);
}

/**
 * @description
 * @param req
 */
export function deal_userDeleteRating(req: types.DealUserRatingDeleteRequest) {
  return service.post<types.GenericResponse<types.DealUserRating>>(
    `/v1/deal_user/rating/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function deal_userUpdateRating(req: types.DealUserRatingUpdateRequest) {
  return service.post<types.GenericResponse<types.DealUserRating>>(
    `/v1/deal_user/rating/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function deal_userAdd(req: types.DealUserAddRequest) {
  return service.post<types.GenericResponse<types.DealUser>>(`/v1/deal_user/add`, req);
}

/**
 * @description
 * @param req
 */
export function deal_userDelete(req: types.DealUserDeleteRequest) {
  return service.post<types.GenericResponse<types.DealUser>>(`/v1/deal_user/delete`, req);
}

/**
 * @description
 * @param req
 */
export function deal_userList(req: types.DealUserListRequest) {
  return service.post<types.GenericResponse<types.DealUserListResponse>>(`/v1/deal_user/list`, req);
}

/**
 * @description
 * @param req
 */
export function deal_userUpdate(req: types.DealUserUpdateRequest) {
  return service.post<types.GenericResponse<types.DealUser>>(`/v1/deal_user/update`, req);
}

/**
 * @description
 * @param req
 */
export function departmentList(req: types.DepartmentListRequest) {
  return service.post<types.GenericResponse<types.DepartmentListResponse>>(
    `/v1/department/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function discountAdd(req: types.DiscountAddRequest) {
  return service.post<types.GenericResponse<types.Discount>>(`/v1/discount/add`, req);
}

/**
 * @description
 * @param req
 */
export function discountCalculateDiscount(req: types.CalculateDiscountRequest) {
  return service.post<types.GenericResponse<types.CalculateDiscountResponse>>(
    `/v1/discount/calculate`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function discountCalculateDiscountAttachment(req: types.CalculateDiscountAttachmentRequest) {
  return service.post<types.GenericResponse<types.CalculateDiscountResponse>>(
    `/v1/discount/calculate-attachment`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function discountDelete(req: types.DiscountDeleteRequest) {
  return service.post<types.GenericResponse<types.Discount>>(`/v1/discount/delete`, req);
}

/**
 * @description
 * @param req
 */
export function discountEligibleDiscount(req: types.EligibleDiscountRequest) {
  return service.post<types.GenericResponse<types.EligibleDiscountResponse>>(
    `/v1/discount/eligible`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function discountGet(req: types.DiscountGetRequest) {
  return service.post<types.GenericResponse<types.Discount>>(`/v1/discount/get`, req);
}

/**
 * @description
 * @param req
 */
export function discountList(req: types.DiscountListRequest) {
  return service.post<types.GenericResponse<types.DiscountListResponse>>(`/v1/discount/list`, req);
}

/**
 * @description
 * @param req
 */
export function discountUpdate(req: types.DiscountUpdateRequest) {
  return service.post<types.GenericResponse<types.Discount>>(`/v1/discount/update`, req);
}

/**
 * @description
 * @param req
 */
export function discount_usageAdd(req: types.DiscountUsageAddRequest) {
  return service.post<types.GenericResponse<types.DiscountUsageResponse>>(
    `/v1/discount_usage/add`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function discount_usageDelete(req: types.DiscountUsageDeleteRequest) {
  return service.post<types.GenericResponse<types.DiscountUsageResponse>>(
    `/v1/discount_usage/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function discount_usageList(req: types.DiscountUsageListRequest) {
  return service.post<types.GenericResponse<types.DiscountUsageListResponse>>(
    `/v1/discount_usage/list`,
    req,
  );
}

/**
 * @description
 * @param params
 */
export function downloadDownloadFile(params: types.DownloadFileRequestParams) {
  return service.get<null>(`/v1/download/file`, params);
}

/**
 * @description
 * @param req
 */
export function economy_depositAdd(req: types.DepositAddRequest) {
  return service.post<types.GenericResponse<types.DepositResponse>>(`/v1/economy/deposit/add`, req);
}

/**
 * @description
 * @param req
 */
export function economy_depositAllocationAdd(req: types.AllocationAddRequest) {
  return service.post<types.GenericResponse<types.DepositAllocation>>(
    `/v1/economy/deposit/allocation/add`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_depositAllocationDelete(req: types.DeleteRequest) {
  return service.post<types.GenericResponse<types.DepositAllocation>>(
    `/v1/economy/deposit/allocation/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_depositCancelConversion(req: types.DeleteRequest) {
  return service.post<types.GenericResponse<types.DepositPayment>>(
    `/v1/economy/deposit/cancel-conversion`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_depositConvertToPayment(req: types.ConvertToPaymentRequest) {
  return service.post<types.GenericResponse<types.DepositPayment>>(
    `/v1/economy/deposit/convert-to-payment`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_depositDelete(req: types.DeleteRequest) {
  return service.post<types.GenericResponse<types.Deposit>>(`/v1/economy/deposit/delete`, req);
}

/**
 * @description
 * @param req
 */
export function economy_depositGet(req: types.GetRequest) {
  return service.post<types.GenericResponse<types.DepositResponse>>(`/v1/economy/deposit/get`, req);
}

/**
 * @description
 * @param req
 */
export function economy_depositList(req: types.DepositListRequest) {
  return service.post<types.GenericResponse<types.DepositListResponse>>(
    `/v1/economy/deposit/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_depositUpdate(req: types.UpdateDepositRequest) {
  return service.post<types.GenericResponse<types.DepositResponse>>(
    `/v1/economy/deposit/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_paymentAdd(req: types.PaymentAddRequest) {
  return service.post<types.GenericResponse<types.PaymentResponse>>(`/v1/economy/payment/add`, req);
}

/**
 * @description
 * @param req
 */
export function economy_paymentDelete(req: types.PaymentDeleteRequest) {
  return service.post<types.GenericResponse<types.PaymentResponse>>(
    `/v1/economy/payment/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_paymentGet(req: types.PaymentGetRequest) {
  return service.post<types.GenericResponse<types.PaymentResponse>>(`/v1/economy/payment/get`, req);
}

/**
 * @description
 * @param req
 */
export function economy_paymentList(req: types.PaymentListRequest) {
  return service.post<types.GenericResponse<types.PaymentListResponse>>(
    `/v1/economy/payment/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_paymentQuery(req: types.PaymentReportDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/economy/payment/query`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_paymentQueryDetail(req: types.PaymentReportDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/economy/payment/query-detail`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_paymentUpdate(req: types.PaymentUpdateRequest) {
  return service.post<types.GenericResponse<types.PaymentResponse>>(
    `/v1/economy/payment/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_payment_allocationAdd(req: types.PaymentAllocationAddRequest) {
  return service.post<types.GenericResponse<types.PaymentAllocationResponse>>(
    `/v1/economy/payment-allocation/add`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_payment_allocationDelete(req: types.PaymentAllocationDeleteRequest) {
  return service.post<types.GenericResponse<types.PaymentAllocationResponse>>(
    `/v1/economy/payment-allocation/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function economy_payment_allocationUpdate(req: types.PaymentAllocationUpdateRequest) {
  return service.post<types.GenericResponse<types.PaymentAllocationResponse>>(
    `/v1/economy/payment-allocation/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function exportExport(req: types.ExportRequest) {
  return service.post<types.GenericResponse<types.ExportJobResponse>>(`/v1/export`, req);
}

/**
 * @description
 * @param req
 */
export function exportDownload(req: types.DownloadJobRequest) {
  return service.post<null>(`/v1/export/download`, req);
}

/**
 * @description
 * @param params
 */
export function fileAdd(params: types.FileAddRequestParams) {
  return service.post<types.GenericResponse<types.FileResponse>>(`/v1/file/add`, params);
}

/**
 * @description
 * @param req
 */
export function fileDelete(req: types.FileDeleteRequest) {
  return service.post<types.GenericResponse<types.FileResponse>>(`/v1/file/delete`, req);
}

/**
 * @description
 * @param req
 */
export function fileGet(req: types.FileGetRequest) {
  return service.post<types.GenericResponse<types.FileResponse>>(`/v1/file/get`, req);
}

/**
 * @description
 * @param req
 */
export function fileList(req: types.FileListRequest) {
  return service.post<types.GenericResponse<types.FileListResponse>>(`/v1/file/list`, req);
}

/**
 * @description
 * @param req
 */
export function fileUpdate(req: types.FileUpdateRequest) {
  return service.post<types.GenericResponse<types.FileResponse>>(`/v1/file/update`, req);
}

/**
 * @description
 * @param req
 */
export function file_usageAdd(req: types.FileUsageAddRequest) {
  return service.post<types.GenericResponse<types.FileUsageResponse>>(`/v1/file_usage/add`, req);
}

/**
 * @description
 * @param req
 */
export function file_usageDelete(req: types.FileUsageDeleteRequest) {
  return service.post<types.GenericResponse<types.FileUsageResponse>>(`/v1/file_usage/delete`, req);
}

/**
 * @description
 * @param req
 */
export function file_usageGet(req: types.FileUsageGetRequest) {
  return service.post<types.GenericResponse<types.FileUsageResponse>>(`/v1/file_usage/get`, req);
}

/**
 * @description
 * @param req
 */
export function file_usageList(req: types.FileUsageListRequest) {
  return service.post<types.GenericResponse<types.FileUsageListResponse>>(
    `/v1/file_usage/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function file_usageUpdate(req: types.FileUsageUpdateRequest) {
  return service.post<types.GenericResponse<types.FileUsageResponse>>(`/v1/file_usage/update`, req);
}

/**
 * @description
 * @param req
 */
export function form_submissionConvertToPerson(req: types.ConvertToPersonRequest) {
  return service.post<types.GenericResponse<types.BulkUpdateFormSubmissionResult>>(
    `/v1/formsubmission/convert-to-person`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function form_submissionDeleteFormSubmission(req: types.FormSubmissionDeleteRequest) {
  return service.post<types.GenericResponse<types.FormSubmissionDeleteResponse>>(
    `/v1/formsubmission/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function form_submissionListFormSubmissions(req: types.FormSubmissionsListRequest) {
  return service.post<types.GenericResponse<types.FormSubmissionListResponse>>(
    `/v1/formsubmission/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function form_submissionQueryFormSubmissions(req: types.FormSubmissionDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/formsubmission/query`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function historyGetHistoryList(req: types.HistoryListRequest) {
  return service.post<types.GenericResponse<types.HistoryListResponse>>(`/api/v1/history`, req);
}

/**
 * @description
 * @param req
 */
export function installmentAdd(req: types.InstallmentAddRequest) {
  return service.post<types.GenericResponse<types.Installment>>(`/v1/installment/add`, req);
}

/**
 * @description
 * @param req
 */
export function installmentDelete(req: types.InstallmentDeleteRequest) {
  return service.post<types.GenericResponse<types.Installment>>(`/v1/installment/delete`, req);
}

/**
 * @description
 * @param req
 */
export function installmentGet(req: types.InstallmentGetRequest) {
  return service.post<types.GenericResponse<types.Installment>>(`/v1/installment/get`, req);
}

/**
 * @description
 * @param req
 */
export function installmentList(req: types.InstallmentListRequest) {
  return service.post<types.GenericResponse<types.InstallmentListResponse>>(
    `/v1/installment/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function installmentGetPartiallyPaid(req: types.InstallmentGetPartiallyPaidRequest) {
  return service.post<types.GenericResponse<types.InstallmentListResponse>>(
    `/v1/installment/partially-paid`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function installmentGetRefund(req: types.InstallmentGetPartiallyPaidRequest) {
  return service.post<types.GenericResponse<types.InstallmentListResponse>>(
    `/v1/installment/refund`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function installmentUpdate(req: types.InstallmentUpdateRequest) {
  return service.post<types.GenericResponse<types.Installment>>(`/v1/installment/update`, req);
}

/**
 * @description
 * @param req
 */
export function installment_planAdd(req: types.InstallmentPlanAddRequest) {
  return service.post<types.GenericResponse<types.InstallmentPlanResponse>>(
    `/v1/installment-plan/add`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function installment_planDelete(req: types.InstallmentPlanDeleteRequest) {
  return service.post<types.GenericResponse<types.InstallmentPlan>>(
    `/v1/installment-plan/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function installment_planGet(req: types.InstallmentPlanGetRequest) {
  return service.post<types.GenericResponse<types.InstallmentPlanResponse>>(
    `/v1/installment-plan/get`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function installment_planList(req: types.InstallmentPlanListRequest) {
  return service.post<types.GenericResponse<types.InstallmentPlanListResponse>>(
    `/v1/installment-plan/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function installment_planListRefundable(req: types.InstallmentPlanRefundableListRequest) {
  return service.post<types.GenericResponse<types.InstallmentPlanListResponse>>(
    `/v1/installment-plan/list-refundable`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function installment_planUpdate(req: types.InstallmentPlanUpdateRequest) {
  return service.post<types.GenericResponse<types.InstallmentPlanResponse>>(
    `/v1/installment-plan/update`,
    req,
  );
}

/**
 * @description
 */
export function locationGet() {
  return service.post<types.GenericResponse<types.LocationResponse>>(`/v1/location`);
}

/**
 * @description
 * @param req
 */
export function materialAdd(req: types.MaterialAddRequest) {
  return service.post<types.GenericResponse<types.Material>>(`/v1/material/add`, req);
}

/**
 * @description
 * @param req
 */
export function materialDelete(req: types.MaterialDeleteRequest) {
  return service.post<null>(`/v1/material/delete`, req);
}

/**
 * @description
 * @param req
 */
export function materialGet(req: types.MaterialGetRequest) {
  return service.post<types.GenericResponse<types.Material>>(`/v1/material/get`, req);
}

/**
 * @description
 * @param req
 */
export function materialList(req: types.MaterialListRequest) {
  return service.post<types.GenericResponse<types.MaterialListResponse>>(`/v1/material/list`, req);
}

/**
 * @description
 * @param req
 */
export function materialUpdate(req: types.MaterialUpdateRequest) {
  return service.post<types.GenericResponse<types.Material>>(`/v1/material/update`, req);
}

/**
 * @description
 * @param req
 */
export function material_usageAdd(req: types.MaterialUsageAddRequest) {
  return service.post<types.GenericResponse<types.MaterialUsage>>(`/v1/material/usage/add`, req);
}

/**
 * @description
 * @param req
 */
export function material_usageDelete(req: types.MaterialUsageDeleteRequest) {
  return service.post<types.GenericResponse<types.MaterialUsage>>(`/v1/material/usage/delete`, req);
}

/**
 * @description
 * @param req
 */
export function material_usageGet(req: types.MaterialUsageGetRequest) {
  return service.post<types.GenericResponse<types.MaterialUsage>>(`/v1/material/usage/get`, req);
}

/**
 * @description
 * @param req
 */
export function material_usageList(req: types.MaterialUsageListRequest) {
  return service.post<types.GenericResponse<types.MaterialUsageListResponse>>(
    `/v1/material/usage/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function material_usageUpdate(req: types.MaterialUsageUpdateRequest) {
  return service.post<types.GenericResponse<types.MaterialUsage>>(`/v1/material/usage/update`, req);
}

/**
 * @description
 * @param req
 */
export function mindComplete(req: types.CompleteRequest) {
  return service.post<types.GenericResponse<types.CompleteResponse>>(`/v1/mind/complete`, req);
}

/**
 * @description
 * @param req
 */
export function mindContentCommand(req: types.ContentCommandRequest) {
  return service.post<types.GenericResponse<types.ContentCommandResponse>>(
    `/v1/mind/content/command`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function mindContentContinue(req: types.ContentModificationRequest) {
  return service.post<types.GenericResponse<types.ContentModificationResponse>>(
    `/v1/mind/content/continue`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function mindContentImprove(req: types.ContentModificationRequest) {
  return service.post<types.GenericResponse<types.ContentModificationResponse>>(
    `/v1/mind/content/improve`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function mindContentLonger(req: types.ContentModificationRequest) {
  return service.post<types.GenericResponse<types.ContentModificationResponse>>(
    `/v1/mind/content/longer`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function mindContentShorter(req: types.ContentModificationRequest) {
  return service.post<types.GenericResponse<types.ContentModificationResponse>>(
    `/v1/mind/content/shorter`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function mindGetCron(req: types.GetCronRequest) {
  return service.post<types.GenericResponse<types.GetCronResponse>>(`/v1/mind/getcron`, req);
}

/**
 * @description
 * @param req
 */
export function noteAdd(req: types.NoteAddRequest) {
  return service.post<types.GenericResponse<types.Note>>(`/v1/note/add`, req);
}

/**
 * @description
 * @param req
 */
export function noteDelete(req: types.NoteDeleteRequest) {
  return service.post<types.GenericResponse<types.Note>>(`/v1/note/delete`, req);
}

/**
 * @description
 * @param req
 */
export function noteList(req: types.NoteListRequest) {
  return service.post<types.GenericResponse<types.NoteListResponse>>(`/v1/note/list`, req);
}

/**
 * @description
 * @param req
 */
export function noteUpdate(req: types.NoteUpdateRequest) {
  return service.post<types.GenericResponse<types.Note>>(`/v1/note/update`, req);
}

/**
 * @description "(Tùy chọn) Xóa một thông báo"
 * @param req
 */
export function notificationDeleteNotification(req: types.DeleteNotificationReq) {
  return service.post<types.GenericResponse<types.DeleteNotificationResp>>(
    `/v1/notification/delete`,
    req,
  );
}

/**
 * @description "Lấy danh sách thông báo cho người dùng đã đăng nhập"
 * @param req
 */
export function notificationListNotifications(req: types.ListNotificationsReq) {
  return service.post<types.GenericResponse<types.ListNotificationsResp>>(
    `/v1/notification/list`,
    req,
  );
}

/**
 * @description "Đánh dấu một hoặc nhiều thông báo cụ thể là đã đọc"
 * @param req
 */
export function notificationMarkNotificationsAsRead(req: types.MarkNotificationsAsReadReq) {
  return service.post<types.GenericResponse<types.MarkNotificationsAsReadResp>>(
    `/v1/notification/read`,
    req,
  );
}

/**
 * @description "Đánh dấu tất cả thông báo chưa đọc của người dùng là đã đọc"
 * @param req
 */
export function notificationMarkAllNotificationsAsRead(req: types.MarkAllNotificationsAsReadReq) {
  return service.post<types.GenericResponse<types.MarkAllNotificationsAsReadResp>>(
    `/v1/notification/read-all`,
    req,
  );
}

/**
 * @description "Lấy số lượng thông báo chưa đọc của người dùng hiện tại"
 * @param req
 */
export function notificationGetUnreadNotificationCount(req: types.GetUnreadNotificationCountReq) {
  return service.post<types.GenericResponse<types.GetUnreadNotificationCountResp>>(
    `/v1/notification/unread-count`,
    req,
  );
}

/**
 * @description
 */
export function operationAll() {
  return service.post<types.GenericResponse<types.OperationAllResponse>>(`/v1/operation/all`);
}

/**
 * @description
 * @param req
 */
export function operation_materialAdd(req: types.OperationMaterialAddRequest) {
  return service.post<types.GenericResponse<types.OperationMaterial>>(
    `/v1/operation/material/add`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function operation_materialDelete(req: types.OperationMaterialDeleteRequest) {
  return service.post<null>(`/v1/operation/material/delete`, req);
}

/**
 * @description
 * @param req
 */
export function operation_materialGet(req: types.OperationMaterialGetRequest) {
  return service.post<types.GenericResponse<types.OperationMaterial>>(
    `/v1/operation/material/get`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function operation_materialList(req: types.OperationMaterialListRequest) {
  return service.post<types.GenericResponse<types.OperationMaterialListResponse>>(
    `/v1/operation/material/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function operation_materialUpdate(req: types.OperationMaterialUpdateRequest) {
  return service.post<types.GenericResponse<types.OperationMaterial>>(
    `/v1/operation/material/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function personAdd(req: types.PersonAddRequest) {
  return service.post<types.GenericResponse<types.PersonResponse>>(`/v1/person/add`, req);
}

/**
 * @description
 * @param req
 */
export function personDelete(req: types.PersonDeleteRequest) {
  return service.post<types.GenericResponse<types.PersonResponse>>(`/v1/person/delete`, req);
}

/**
 * @description
 * @param req
 */
export function personGetExpectedTasks(req: types.PersonGetRequest) {
  return service.post<types.GenericResponse<types.ExpectedTasksResponse>>(
    `/v1/person/expect-task`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function personGet(req: types.PersonGetRequest) {
  return service.post<types.GenericResponse<types.PersonResponse>>(`/v1/person/get`, req);
}

/**
 * @description
 * @param req
 */
export function personIsPersonIn(req: types.PersonGetRequest) {
  return service.post<types.GenericResponse<boolean>>(`/v1/person/is-person-in`, req);
}

/**
 * @description
 * @param req
 */
export function personList(req: types.PersonListRequest) {
  return service.post<types.GenericResponse<types.PersonListResponse>>(`/v1/person/list`, req);
}

/**
 * @description
 * @param req
 */
export function personQuery(req: types.PersonDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(`/v1/person/query`, req);
}

/**
 * @description
 * @param req
 */
export function personTimeline(req: types.PersonTimelineRequest) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/person/timeline`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function personUpdate(req: types.PersonUpdateRequest) {
  return service.post<types.GenericResponse<types.PersonResponse>>(`/v1/person/update`, req);
}

/**
 * @description
 * @param req
 */
export function personHistoryMessage(req: types.HistoryRequest) {
  return service.post<types.GenericResponse<types.HistoryResponse>>(
    `/v1/person/message/history`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function personSendMessage(req: types.SendMessageRequest) {
  return service.post<types.GenericResponse<types.SendMessageResponse>>(
    `/v1/person/message/send`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function personMessageTemplate(req: types.MessageTemplateRequest) {
  return service.post<types.GenericResponse<types.MessageTemplateResponse>>(
    `/v1/person/message/template`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function person_assignmentAdd(req: types.PersonAssignmentAddRequest) {
  return service.post<types.GenericResponse<types.PersonAssignment>>(
    `/v1/person_assignment/add`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function person_assignmentDelete(req: types.PersonAssignmentDeleteRequest) {
  return service.post<types.GenericResponse<types.PersonAssignment>>(
    `/v1/person_assignment/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function person_assignmentUpdate(req: types.PersonAssignmentUpdateRequest) {
  return service.post<types.GenericResponse<types.PersonAssignment>>(
    `/v1/person_assignment/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function person_dataClearData(req: types.ClearDataRequest) {
  return service.post<types.GenericResponse<types.CommonResponse>>(`/v1/person/data/clear`, req);
}

/**
 * @description
 * @param req
 */
export function person_dataGetData(req: types.GetDataRequest) {
  return service.post<types.GenericResponse<types.DataResponse>>(`/v1/person/data/get`, req);
}

/**
 * @description
 * @param req
 */
export function person_dataSetData(req: types.SetDataRequest) {
  return service.post<types.GenericResponse<types.CommonResponse>>(`/v1/person/data/set`, req);
}

/**
 * @description
 * @param req
 */
export function pipelineAdd(req: types.PipelineAddRequest) {
  return service.post<types.GenericResponse<types.PipelineResponse>>(`/v1/pipeline/add`, req);
}

/**
 * @description
 * @param req
 */
export function pipelineDelete(req: types.PipelineDeleteRequest) {
  return service.post<types.GenericResponse<types.PipelineResponse>>(`/v1/pipeline/delete`, req);
}

/**
 * @description
 * @param req
 */
export function pipelineGet(req: types.PipelineGetRequest) {
  return service.post<types.GenericResponse<types.PipelineResponse>>(`/v1/pipeline/get`, req);
}

/**
 * @description
 * @param req
 */
export function pipelineList(req: types.PipelineListRequest) {
  return service.post<types.GenericResponse<types.PipelineListResponse>>(`/v1/pipeline/list`, req);
}

/**
 * @description
 * @param req
 */
export function pipelineSwitchDeal(req: types.SwitchDealRequest) {
  return service.post<types.GenericResponse<types.SwitchDealResponse>>(
    `/v1/pipeline/switch-deal`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function pipelineUpdate(req: types.PipelineUpdateRequest) {
  return service.post<types.GenericResponse<types.PipelineResponse>>(`/v1/pipeline/update`, req);
}

/**
 * @description
 * @param req
 */
export function productAdd(req: types.ProductAddRequest) {
  return service.post<types.GenericResponse<types.ProductResponse>>(`/v1/product/add`, req);
}

/**
 * @description
 * @param req
 */
export function productDelete(req: types.ProductDeleteRequest) {
  return service.post<types.GenericResponse<types.ProductResponse>>(`/v1/product/delete`, req);
}

/**
 * @description
 * @param req
 */
export function productGet(req: types.ProductGetRequest) {
  return service.post<types.GenericResponse<types.ProductResponse>>(`/v1/product/get`, req);
}

/**
 * @description
 * @param req
 */
export function productList(req: types.ProductListRequest) {
  return service.post<types.GenericResponse<types.ProductListResponse>>(`/v1/product/list`, req);
}

/**
 * @description
 * @param req
 */
export function productUpdate(req: types.ProductUpdateRequest) {
  return service.post<types.GenericResponse<types.ProductResponse>>(`/v1/product/update`, req);
}

/**
 * @description
 * @param req
 */
export function product_operationAdd(req: types.ProductOperationAddRequest) {
  return service.post<types.GenericResponse<types.ProductOperation>>(
    `/v1/product_operation/add`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function product_operationDelete(req: types.ProductOperationDeleteRequest) {
  return service.post<types.GenericResponse<types.ProductOperation>>(
    `/v1/product_operation/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function publicFormAdd(req: types.FormAddRequest) {
  return service.post<types.GenericResponse<types.FormResponse>>(`/v1/public/form/add`, req);
}

/**
 * @description Raw Query
 * @param req
 */
export function queryRawQuery(req: types.RawQueryRequest) {
  return service.post<types.GenericResponse<types.RawQueryResponse>>(`/v1/query/raw`, req);
}

/**
 * @description
 * @param req
 */
export function queryQuery(req: types.DynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(`/v1/query/query`, req);
}

/**
 * @description
 * @param req
 */
export function queryv2QueryV2(req: types.DynamicQueryV2) {
  return service.post<types.GenericResponse<types.DynamicQueryV2Response>>(
    `/v1/query-v2/query`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function referralAdd(req: types.ReferralAddRequest) {
  return service.post<types.GenericResponse<types.Referral>>(`/v1/referral/add`, req);
}

/**
 * @description
 * @param req
 */
export function referralDelete(req: types.ReferralDeleteRequest) {
  return service.post<types.GenericResponse<types.Referral>>(`/v1/referral/delete`, req);
}

/**
 * @description
 * @param req
 */
export function referralSearchReferrer(req: types.SearchRequest) {
  return service.post<types.GenericResponse<types.SearchResponse>>(
    `/v1/referral/search-referrer`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function referralUpdate(req: types.ReferralUpdateRequest) {
  return service.post<types.GenericResponse<types.Referral>>(`/v1/referral/update`, req);
}

/**
 * @description
 * @param req
 */
export function scheduleAdd(req: types.ScheduleAddRequest) {
  return service.post<types.GenericResponse<types.ScheduleResponse>>(`/v1/schedule/add`, req);
}

/**
 * @description
 * @param req
 */
export function scheduleDelete(req: types.ScheduleDeleteRequest) {
  return service.post<types.GenericResponse<types.ScheduleResponse>>(`/v1/schedule/delete`, req);
}

/**
 * @description
 * @param req
 */
export function schedulegetWorkSchedule(req: types.ScheduleRequest) {
  return service.post<types.GenericResponse<types.ScheduleListResponse>>(
    `/v1/schedule/get-work-schedule`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function scheduleList(req: types.ScheduleRequest) {
  return service.post<types.GenericResponse<types.ScheduleListResponse>>(`/v1/schedule/list`, req);
}

/**
 * @description
 * @param req
 */
export function scheduleUpdate(req: types.ScheduleUpdateRequest) {
  return service.post<types.GenericResponse<types.Schedule>>(`/v1/schedule/update`, req);
}

/**
 * @description
 * @param req
 */
export function settingAdd(req: types.SettingAddRequest) {
  return service.post<types.GenericResponse<types.Setting>>(`/v1/setting/add`, req);
}

/**
 * @description
 * @param req
 */
export function settingDelete(req: types.SettingDeleteRequest) {
  return service.post<types.GenericResponse<types.Setting>>(`/v1/setting/delete`, req);
}

/**
 * @description
 * @param req
 */
export function settingList(req: types.SettingListRequest) {
  return service.post<types.GenericResponse<types.SettingListResponse>>(`/v1/setting/list`, req);
}

/**
 * @description
 * @param req
 */
export function settingSync(req: types.SettingSyncRequest) {
  return service.post<types.GenericResponse<types.Setting>>(`/v1/setting/sync`, req);
}

/**
 * @description
 * @param req
 */
export function settingUpdate(req: types.SettingUpdateRequest) {
  return service.post<types.GenericResponse<types.Setting>>(`/v1/setting/update`, req);
}

/**
 * @description
 * @param req
 */
export function stageAdd(req: types.StageAddRequest) {
  return service.post<types.GenericResponse<types.StageResponse>>(`/v1/stage/add`, req);
}

/**
 * @description
 * @param req
 */
export function stageDelete(req: types.StageDeleteRequest) {
  return service.post<types.GenericResponse<types.StageResponse>>(`/v1/stage/delete`, req);
}

/**
 * @description
 * @param req
 */
export function stageGet(req: types.StageGetRequest) {
  return service.post<types.GenericResponse<types.StageResponse>>(`/v1/stage/get`, req);
}

/**
 * @description
 * @param req
 */
export function stageList(req: types.StageListRequest) {
  return service.post<types.GenericResponse<types.StageListResponse>>(`/v1/stage/list`, req);
}

/**
 * @description
 * @param req
 */
export function stageUpdate(req: types.StageUpdateRequest) {
  return service.post<types.GenericResponse<types.StageResponse>>(`/v1/stage/update`, req);
}

/**
 * @description
 * @param req
 */
export function stageUpdateStage(req: types.StageUpdateMetaRequest) {
  return service.post<types.GenericResponse<types.StageResponse>>(`/v1/stage/update-stage`, req);
}

/**
 * @description
 * @param req
 */
export function syncSyncDataGetFly(req: types.SyncDataRequest) {
  return service.post<null>(`/v1/sync/data-getfly`, req);
}

/**
 * @description
 * @param req
 */
export function syncSyncDataUp(req: types.SyncDataRequest) {
  return service.post<null>(`/v1/sync/data-up`, req);
}

/**
 * @description
 */
export function syncSyncTaskGetFly() {
  return service.post<null>(`/v1/sync/task-getfly`);
}

/**
 * @description
 * @param req
 */
export function tagAdd(req: types.TagAddRequest) {
  return service.post<types.GenericResponse<types.TagResponse>>(`/v1/tag/add`, req);
}

/**
 * @description
 * @param req
 */
export function tagDelete(req: types.TagDeleteRequest) {
  return service.post<null>(`/v1/tag/delete`, req);
}

/**
 * @description
 * @param req
 */
export function tagGet(req: types.TagGetRequest) {
  return service.post<types.GenericResponse<types.TagResponse>>(`/v1/tag/get`, req);
}

/**
 * @description
 * @param req
 */
export function tagList(req: types.TagListRequest) {
  return service.post<types.GenericResponse<types.TagListResponse>>(`/v1/tag/list`, req);
}

/**
 * @description
 * @param req
 */
export function tagUpdate(req: types.TagUpdateRequest) {
  return service.post<types.GenericResponse<types.TagResponse>>(`/v1/tag/update`, req);
}

/**
 * @description
 * @param req
 */
export function tag_dealAdd(req: types.TagDealAddRequest) {
  return service.post<types.GenericResponse<types.TagDeal>>(`/v1/tag/deal/add`, req);
}

/**
 * @description
 * @param req
 */
export function tag_dealDelete(req: types.TagDealDeleteRequest) {
  return service.post<types.GenericResponse<types.TagDeal>>(`/v1/tag/deal/delete`, req);
}

/**
 * @description
 * @param req
 */
export function tag_dealUpdate(req: types.TagDealUpdateRequest) {
  return service.post<types.GenericResponse<types.TagDeal>>(`/v1/tag/deal/update`, req);
}

/**
 * @description
 * @param req
 */
export function tag_personAdd(req: types.TagPersonAddRequest) {
  return service.post<types.GenericResponse<types.TagPerson>>(`/v1/tag/person/add`, req);
}

/**
 * @description
 * @param req
 */
export function tag_personDelete(req: types.TagPersonDeleteRequest) {
  return service.post<types.GenericResponse<types.TagPerson>>(`/v1/tag/person/delete`, req);
}

/**
 * @description
 * @param req
 */
export function tag_personUpdate(req: types.TagPersonUpdateRequest) {
  return service.post<types.GenericResponse<types.TagPerson>>(`/v1/tag/person/update`, req);
}

/**
 * @description
 * @param req
 */
export function taskAdd(req: types.TaskAddRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/add`, req);
}

/**
 * @description
 * @param req
 */
export function taskBulkDelete(req: types.BulkDeleteRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/bulk-delete`, req);
}

/**
 * @description
 * @param req
 */
export function taskBulkUpdate(req: types.BulkUpdateTasksRequest) {
  return service.post<types.GenericResponse<types.BulkUpdateResult>>(`/v1/task/bulk-update`, req);
}

/**
 * @description
 * @param req
 */
export function taskDelete(req: types.TaskDeleteRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/delete`, req);
}

/**
 * @description
 * @param req
 */
export function taskGet(req: types.TaskGetRequest) {
  return service.post<types.GenericResponse<types.TaskResponse>>(`/v1/task/get`, req);
}

/**
 * @description
 * @param req
 */
export function taskList(req: types.TaskListRequest) {
  return service.post<types.GenericResponse<types.TaskListResponse>>(`/v1/task/list`, req);
}

/**
 * @description
 * @param req
 */
export function taskQuery(req: types.TaskDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(`/v1/task/query`, req);
}

/**
 * @description
 * @param req
 */
export function taskUpdate(req: types.TaskUpdateRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/update`, req);
}

/**
 * @description
 * @param req
 */
export function taskAddNote(req: types.TaskNoteAddRequest) {
  return service.post<types.GenericResponse<types.TaskNote>>(`/v1/task/note/add`, req);
}

/**
 * @description
 * @param req
 */
export function taskDeleteNote(req: types.TaskNoteDeleteRequest) {
  return service.post<types.GenericResponse<types.TaskNote>>(`/v1/task/note/delete`, req);
}

/**
 * @description
 * @param req
 */
export function taskListNote(req: types.TaskNoteListRequest) {
  return service.post<types.GenericResponse<types.TaskNoteListResponse>>(`/v1/task/note/list`, req);
}

/**
 * @description
 * @param req
 */
export function taskUpdateNote(req: types.TaskNoteUpdateRequest) {
  return service.post<types.GenericResponse<types.TaskNote>>(`/v1/task/note/update`, req);
}

/**
 * @description
 * @param req
 */
export function task_assignmentAdd(req: types.TaskAssignmentAddRequest) {
  return service.post<types.GenericResponse<types.TaskAssignment>>(`/v1/task_assignment/add`, req);
}

/**
 * @description
 * @param req
 */
export function task_assignmentAssignTasks(req: types.AssignTasksRequest) {
  return service.post<types.GenericResponse<types.TaskAssignment>>(
    `/v1/task_assignment/assign-tasks`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function task_assignmentDelete(req: types.TaskAssignmentDeleteRequest) {
  return service.post<types.GenericResponse<types.TaskAssignment>>(
    `/v1/task_assignment/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function task_assignmentQuery(req: types.TaskAssignmentDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/task_assignment/query`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function task_assignmentUpdate(req: types.TaskAssignmentUpdateRequest) {
  return service.post<types.GenericResponse<types.TaskAssignment>>(
    `/v1/task_assignment/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function task_departmentAdd(req: types.TaskDepartmentAddRequest) {
  return service.post<types.GenericResponse<types.TaskDepartment>>(`/v1/task_department/add`, req);
}

/**
 * @description
 * @param req
 */
export function task_departmentDelete(req: types.TaskDepartmentDeleteRequest) {
  return service.post<types.GenericResponse<types.TaskDepartment>>(
    `/v1/task_department/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function task_departmentQuery(req: types.TaskDepartmentDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(
    `/v1/task_department/query`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function task_departmentUpdate(req: types.TaskDepartmentUpdateRequest) {
  return service.post<types.GenericResponse<types.TaskDepartment>>(
    `/v1/task_department/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function taxonomyTermAdd(req: types.TermAddRequest) {
  return service.post<types.GenericResponse<types.TermAddResponse>>(`/v1/taxonomy/term-add`, req);
}

/**
 * @description
 * @param req
 */
export function taxonomyTermDelete(req: types.TermGetRequest) {
  return service.post<types.GenericResponse<types.TermAddResponse>>(
    `/v1/taxonomy/term-delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function taxonomyTermGet(req: types.TermGetRequest) {
  return service.post<types.GenericResponse<types.TermGetResponse>>(`/v1/taxonomy/term-get`, req);
}

/**
 * @description
 * @param req
 */
export function taxonomyTermList(req: types.TermGetRequest) {
  return service.post<types.GenericResponse<types.TermListReponse>>(`/v1/taxonomy/term-list`, req);
}

/**
 * @description
 * @param req
 */
export function taxonomyTermUpdate(req: types.TermUpdateRequest) {
  return service.post<types.GenericResponse<types.TermAddResponse>>(
    `/v1/taxonomy/term-update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function trackActiveTrack(req: types.ActiveTrackRequest) {
  return service.post<types.GenericResponse<types.Track>>(`/v1/track/active`, req);
}

/**
 * @description
 * @param req
 */
export function trackAdd(req: types.TrackAddRequest) {
  return service.post<types.GenericResponse<types.TrackResponse>>(`/v1/track/add`, req);
}

/**
 * @description
 * @param req
 */
export function trackCheckout(req: types.TrackCheckoutRequest) {
  return service.post<types.GenericResponse<types.Track>>(`/v1/track/checkout`, req);
}

/**
 * @description
 * @param req
 */
export function trackDelete(req: types.TrackDeleteRequest) {
  return service.post<types.GenericResponse<types.Track>>(`/v1/track/delete`, req);
}

/**
 * @description
 * @param req
 */
export function trackList(req: types.TrackListRequest) {
  return service.post<types.GenericResponse<types.TrackListResponse>>(`/v1/track/list`, req);
}

/**
 * @description
 * @param req
 */
export function trackQuery(req: types.TrackDynamicQuery) {
  return service.post<types.GenericResponse<types.DynamicQueryResponse>>(`/v1/track/query`, req);
}

/**
 * @description
 * @param req
 */
export function trackUpdate(req: types.TrackUpdateRequest) {
  return service.post<types.GenericResponse<types.TrackResponse>>(`/v1/track/update`, req);
}

/**
 * @description
 * @param req
 */
export function userAdd(req: types.UserAddRequest) {
  return service.post<types.GenericResponse<types.UserResponse>>(`/v1/user/add`, req);
}

/**
 * @description
 * @param req
 */
export function userDelete(req: types.UserDeleteRequest) {
  return service.post<null>(`/v1/user/delete`, req);
}

/**
 * @description
 * @param req
 */
export function userGet(req: types.UserGetRequest) {
  return service.post<types.GenericResponse<types.UserResponse>>(`/v1/user/get`, req);
}

/**
 * @description
 * @param req
 */
export function userList(req: types.UserListRequest) {
  return service.post<types.GenericResponse<types.UserListResponse>>(`/v1/user/list`, req);
}

/**
 * @description
 * @param req
 */
export function userUpdate(req: types.UserUpdateRequest) {
  return service.post<types.GenericResponse<types.UserResponse>>(`/v1/user/update`, req);
}

/**
 * @description
 * @param req
 */
export function user_dataClearData(req: types.ClearUserDataRequest) {
  return service.post<types.GenericResponse<types.CommonResponse>>(`/v1/user/data/clear`, req);
}

/**
 * @description
 * @param req
 */
export function user_dataGetData(req: types.GetUserDataRequest) {
  return service.post<types.GenericResponse<types.UserDataResponse>>(`/v1/user/data/get`, req);
}

/**
 * @description
 * @param req
 */
export function user_dataSetData(req: types.SetUserDataRequest) {
  return service.post<types.GenericResponse<types.CommonResponse>>(`/v1/user/data/set`, req);
}

/**
 * @description
 * @param req
 */
export function user_dataSetArrayData(req: types.SetUserDataRequest) {
  return service.post<types.GenericResponse<types.CommonResponse>>(`/v1/user/data/set-array`, req);
}

/**
 * @description
 * @param req
 */
export function user_statPerformance(req: types.StatRequest) {
  return service.post<types.GenericResponse<types.PerformanceStatResponse>>(
    `/v1/user/stat/performance`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function user_statPersonStat(req: types.StatRequest) {
  return service.post<types.GenericResponse<types.PersonStatResponse>>(`/v1/user/stat/person`, req);
}

/**
 * @description
 * @param req
 */
export function user_statSchedule(req: types.StatRequest) {
  return service.post<types.GenericResponse<types.ScheduleStatResponse>>(
    `/v1/user/stat/schedule`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function user_statTaskStat(req: types.StatRequest) {
  return service.post<types.GenericResponse<types.TaskStatResponse>>(`/v1/user/stat/task`, req);
}

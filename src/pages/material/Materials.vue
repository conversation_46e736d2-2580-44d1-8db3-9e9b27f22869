<template>
  <MaterialFormDrawer
    v-model:visible="showMaterialForm"
    :material-id="selectedMaterialId"
    @success="handleFormSuccess"
  />

  <div class="intro-y mt-5">
    <DataTable
      title="Danh sách nguyên liệu"
      :columns="columns"
      :data="tableState.materials"
      :loading="false"
      :total-records="tableState.total"
      paginator
      :rows="perPage"
      v-model:filters="filters"
      @page="handlePageChange"
      :show-actions="{ edit: true, delete: true }"
      @on-edit="handleEdit"
      @on-delete="handleDelete"
      size="small"
      striped-rows
    >
      <template #left-header>
        <div class="flex items-center gap-2">
          <span class="text-base font-medium"> Danh sách nguyên liệu ({{ tableState.total }}) </span>
        </div>
      </template>

      <template #right-header>
        <Button severity="primary" icon="pi pi-plus" label="Thêm nguyên liệu" @click="handleCreate" />
      </template>

      <template #name="{ data }">
        <div class="flex items-center gap-2">
          <i class="pi pi-box text-blue-500"></i>
          <div>
            <div class="font-medium">{{ data.name }}</div>
            <div v-if="data.code" class="text-xs text-gray-500">{{ data.code }}</div>
          </div>
        </div>
      </template>

      <template #cost_price="{ data }">
        <Money v-if="data.cost_price" :amount="data.cost_price" />
        <span v-else class="text-gray-400">-</span>
      </template>

      <template #unit="{ data }">
        <Tag :value="data.unit" severity="info" />
      </template>

      <template #status="{ data }">
        <Tag
          :value="data.status === 1 ? 'Hoạt động' : 'Không hoạt động'"
          :severity="data.status === 1 ? 'success' : 'danger'"
        />
      </template>

      <template #packaging_specification="{ data }">
        <span v-if="data.packaging_specification" class="text-sm">{{ data.packaging_specification }}</span>
        <span v-else class="text-gray-400">-</span>
      </template>

      <template #description="{ data }">
        <span v-if="data.description" class="text-sm">{{ data.description }}</span>
        <span v-else class="text-gray-400">-</span>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import { computed, onMounted, ref, watch } from "vue";

import type { Material, MaterialListResponse } from "@/api/bcare-types-v2";
import Money from "@/base-components/Money.vue";
import { type ColumnDefinition, DataTable } from "@/components/DataTable";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { useFilterList } from "@/hooks/useFilterList";
import useMaterial from "@/hooks/useMaterial";

import MaterialFormDrawer from "./components/MaterialFormDrawer.vue";

const { listMaterials, deleteMaterial } = useMaterial({ autoLoad: false });

// State
const tableState = ref<MaterialListResponse>({
  total: 0,
  total_page: 0,
  materials: [],
});
const perPage = ref<number>(10);
const currentPage = ref(1);

// Column definitions
const columns = computed<ColumnDefinition<Material>[]>(() => [
  {
    field: "name",
    header: "Tên nguyên liệu",
    filterType: "text",
    filterPlaceholder: "Tìm theo tên",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "cost_price",
    header: "Giá / đơn vị",
    showFilterMenu: false,
  },
  {
    field: "unit",
    header: "Đơn vị tính",
    filterType: "text",
    filterPlaceholder: "Tìm theo đơn vị",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "packaging_specification",
    header: "Quy cách đóng gói",
    showFilterMenu: false,
  },
  {
    field: "status",
    header: "Trạng thái",
    filterType: "select",
    filterPlaceholder: "Chọn trạng thái",
    filterMatchMode: FilterMatchMode.EQUALS,
    showFilterMenu: false,
    filterOptions: [
      { title: "Hoạt động", value: 1 },
      { title: "Không hoạt động", value: 0 },
    ],
  },
  {
    field: "description",
    header: "Mô tả",
    showFilterMenu: false,
  },
]);

// Filter handling
const {
  filters,
  currentFilterPayload,
  handlePageChange: baseHandlePageChange,
  handlePerPageChange,
} = useFilterList({
  defaultFilters: {},
  onFilterChange: loadList,
});

const handlePageChange = (event: any) => {
  currentPage.value = event.page + 1;
  baseHandlePageChange(event);
};

const loadList = async (filters: Record<string, any>) => {
  try {
    const response = await listMaterials({
      ...filters,
      page: currentPage.value,
      page_size: perPage.value,
    });
    if (response) {
      tableState.value = response;
    }
  } catch (error) {
    console.error("Error loading materials:", error);
  }
};

const { confirm } = useConfirmTippy();

const handleDelete = async (material: Material, e?: MouseEvent) => {
  confirm(e, {
    title: "Xác nhận xóa nguyên liệu",
    icon: "pi pi-trash",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      await deleteMaterial({ id: material.id, name: material.name });
      loadList(currentFilterPayload.value);
    },
  });
};

// Lifecycle
onMounted(() => {
  // Không cần gọi loadList ở đây
});

// Watchers
watch(perPage, (newPerPage) => {
  handlePerPageChange(newPerPage);
});

// Thêm watcher để theo dõi sự thay đổi của filters
watch(
  filters,
  () => {
    // Khi filters thay đổi, reset về trang 1
    currentPage.value = 1;
  },
  { deep: true },
);

const showMaterialForm = ref(false);
const selectedMaterialId = ref<number>();

const handleCreate = () => {
  showMaterialForm.value = true;
};

const handleEdit = (material: Material) => {
  selectedMaterialId.value = material.id;
  showMaterialForm.value = true;
};

const handleFormSuccess = () => {
  loadList(currentFilterPayload.value);
};
</script>
